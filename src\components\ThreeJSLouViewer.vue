<template>
  <div class="h-full w-full" ref="container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import * as THREE from "three";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
// 导入轨道控制器
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
import { GUI } from "three/examples/jsm/libs/lil-gui.module.min.js";

// 定义事件发射器
const emit = defineEmits<{
  "switch-to-dashboard2": [];
}>();

// 获取容器引用
const container = ref<HTMLDivElement>();

// Three.js 相关变量
let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let poolModel: THREE.Group | null = null;
let animationId: number;
let controls: OrbitControls;
let gui: GUI;
let raycaster: THREE.Raycaster;
let mouse: THREE.Vector2;

// 初始化 Three.js
function initThreeJS() {
  if (!container.value) return;

  // 获取容器尺寸
  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  // 创建场景
  scene = new THREE.Scene();

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    45, // 视角
    width / height, // 宽高比
    0.1, // 近平面
    1000 // 远平面
  );

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(width, height);
  renderer.setPixelRatio(window.devicePixelRatio);

  // 将渲染器添加到容器中
  container.value.appendChild(renderer.domElement);

  // 初始化射线投射器和鼠标向量
  raycaster = new THREE.Raycaster();
  mouse = new THREE.Vector2();

  // 添加鼠标点击事件监听器
  renderer.domElement.addEventListener("click", onMouseClick, false);

  // 添加光源
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(10, 10, 5);
  scene.add(directionalLight);

  // 加载FBX模型
  loadPoolModel();

  // 设置相机位置
  camera.position.set(7, 3, 0.1);
  camera.lookAt(0, 0, 0);

  // 创建轨道控制器
  controls = new OrbitControls(camera, renderer.domElement);

  // 设置控制器的目标点与lookAt一致
  controls.target.set(0, 0.3, 0);
  controls.update();

  // 创建相机调试GUI
  setupCameraDebugGUI();

  // 开始动画
  animate();
}

// 设置相机调试GUI
function setupCameraDebugGUI() {
  gui = new GUI();

  // 相机位置参数
  const cameraParams = {
    positionX: camera.position.x,
    positionY: camera.position.y,
    positionZ: camera.position.z,
    targetX: 0,
    targetY: 0,
    targetZ: 0,
    // 显示当前相机信息的函数
    logCameraInfo() {
      console.log("=== 当前相机参数 ===");
      console.log(
        `相机位置: camera.position.set(${camera.position.x.toFixed(
          2
        )}, ${camera.position.y.toFixed(2)}, ${camera.position.z.toFixed(2)});`
      );
      console.log(
        `相机目标: camera.lookAt(${controls.target.x.toFixed(
          2
        )}, ${controls.target.y.toFixed(2)}, ${controls.target.z.toFixed(2)});`
      );
      console.log(
        `控制器目标: controls.target.set(${controls.target.x.toFixed(
          2
        )}, ${controls.target.y.toFixed(2)}, ${controls.target.z.toFixed(2)});`
      );
    },
    // 重置相机位置
    resetCamera() {
      camera.position.set(0, 1, 0.7);
      controls.target.set(0, 0.3, 0);
      controls.update();
      updateGUIValues();
    },
    // 全屏切换
    toggleFullscreen() {
      if (!document.fullscreenElement) {
        document.body.requestFullscreen();
      } else {
        document.exitFullscreen();
      }
    },
  };

  // 更新GUI显示值的函数
  function updateGUIValues() {
    cameraParams.positionX = camera.position.x;
    cameraParams.positionY = camera.position.y;
    cameraParams.positionZ = camera.position.z;
    cameraParams.targetX = controls.target.x;
    cameraParams.targetY = controls.target.y;
    cameraParams.targetZ = controls.target.z;
    // 手动更新GUI控制器显示
    gui
      .controllersRecursive()
      .forEach((controller) => controller.updateDisplay());
  }

  // 相机位置控制
  const cameraFolder = gui.addFolder("相机位置");
  cameraFolder
    .add(cameraParams, "positionX", -20, 20, 0.1)
    .onChange((value) => {
      camera.position.x = value;
    });
  cameraFolder
    .add(cameraParams, "positionY", -20, 20, 0.1)
    .onChange((value) => {
      camera.position.y = value;
    });
  cameraFolder
    .add(cameraParams, "positionZ", -20, 20, 0.1)
    .onChange((value) => {
      camera.position.z = value;
    });

  // 相机目标控制
  const targetFolder = gui.addFolder("相机目标");
  targetFolder.add(cameraParams, "targetX", -10, 10, 0.1).onChange((value) => {
    controls.target.x = value;
    controls.update();
  });
  targetFolder.add(cameraParams, "targetY", -10, 10, 0.1).onChange((value) => {
    controls.target.y = value;
    controls.update();
  });
  targetFolder.add(cameraParams, "targetZ", -10, 10, 0.1).onChange((value) => {
    controls.target.z = value;
    controls.update();
  });

  // 功能按钮
  gui.add(cameraParams, "logCameraInfo").name("📋 获取当前参数");
  gui.add(cameraParams, "resetCamera").name("🔄 重置相机");
  gui.add(cameraParams, "toggleFullscreen").name("🖥️ 全屏切换");

  // 监听控制器变化，实时更新GUI显示
  controls.addEventListener("change", updateGUIValues);

  // 展开文件夹
  cameraFolder.open();
  targetFolder.open();
}

// 鼠标点击事件处理函数
function onMouseClick(event: MouseEvent) {
  if (!container.value || !camera || !scene) return;

  // 计算鼠标位置（标准化设备坐标）
  const rect = container.value.getBoundingClientRect();
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  // 更新射线投射器
  raycaster.setFromCamera(mouse, camera);

  // 检测与模型的交集
  if (poolModel) {
    const intersects = raycaster.intersectObjects(poolModel.children, true);

    if (intersects.length > 0) {
      const clickedObject = intersects[0].object;

      // 检查是否点击了楼层
      if (isBuildingFloor(clickedObject)) {
        console.log("点击了楼层:", clickedObject.parent?.name);
        emit("switch-to-dashboard2");
      }
    }
  }
}

// 检查对象是否是楼层
function isBuildingFloor(object: THREE.Object3D): boolean {
  // 检查对象名称是否包含楼层标识
  const name = object.name.toLowerCase();

  // 匹配 "1栋2层"、"1栋3层" 等格式
  const floorPattern = /\d+栋\d+层/;

  // 检查当前对象名称
  if (floorPattern.test(name)) {
    return true;
  }

  // 检查父级对象名称
  let parent = object.parent;
  while (parent) {
    if (floorPattern.test(parent.name.toLowerCase())) {
      return true;
    }
    parent = parent.parent;
  }

  return false;
}

// 打印模型结构（用于调试）
function printModelStructure(object: THREE.Object3D, depth = 0) {
  const indent = "  ".repeat(depth);
  console.log(`${indent}${object.name || "unnamed"} (${object.type})`);

  // 递归打印子对象
  object.children.forEach((child) => {
    printModelStructure(child, depth + 1);
  });
}

// 加载GLB模型
function loadPoolModel() {
  const loader = new GLTFLoader();

  // 使用指定路径
  const modelPath = "/yuanqu.glb";
  console.log(`Loading model from: ${modelPath}`);

  loader.load(
    modelPath,
    (gltf) => {
      poolModel = gltf.scene;

      // 调整模型大小和位置 - 增大缩放比例
      gltf.scene.scale.setScalar(0.07);
      gltf.scene.position.set(0, 0, 0);

      // 将模型旋转90度（绕Y轴旋转）
      gltf.scene.rotation.y = Math.PI / 4;

      // 添加到场景
      scene.add(gltf.scene);

      console.log("🚀 ~ loadPoolModel ~ gltf.scene:", gltf.scene);
      console.log(`Model loaded successfully from: ${modelPath}`);

      // 打印模型结构，查找楼层对象
      // printModelStructure(gltf.scene);
    },
    (progress) => {
      // console.log(
      //   `Loading progress:`,
      //   (progress.loaded / progress.total) * 100 + "%"
      // );
    },
    (error) => {
      console.error(`Error loading model:`, error);
    }
  );
}

// 渲染函数
function animate() {
  animationId = requestAnimationFrame(animate);
  renderer.render(scene, camera);
}

// 处理窗口大小变化
function handleResize() {
  if (!container.value || !camera || !renderer) return;

  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  camera.aspect = width / height;
  camera.updateProjectionMatrix();
  renderer.setSize(width, height);
}

// 清理资源
function cleanup() {
  if (animationId) {
    cancelAnimationFrame(animationId);
  }

  // 清理模型
  if (poolModel) {
    scene.remove(poolModel);
    poolModel = null;
  }

  // 清理GUI
  if (gui) {
    gui.destroy();
  }

  // 清理控制器
  if (controls) {
    controls.dispose();
  }

  // 移除鼠标事件监听器
  if (renderer && renderer.domElement) {
    renderer.domElement.removeEventListener("click", onMouseClick, false);
  }

  if (renderer) {
    renderer.dispose();
  }

  window.removeEventListener("resize", handleResize);
}

// 组件挂载时初始化
onMounted(() => {
  initThreeJS();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});
</script>
